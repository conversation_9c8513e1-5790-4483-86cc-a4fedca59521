import React, { createContext, useContext, useEffect, useState } from 'react'
import { User, Session } from '@supabase/supabase-js'
import { supabase, UserProfile } from '@/lib/supabase'

interface AuthContextType {
  user: User | null
  profile: UserProfile | null
  session: Session | null
  loading: boolean
  signInWithGoogle: () => Promise<void>
  signOut: () => Promise<void>
  refreshProfile: () => Promise<void>
  clearAuthData: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)

  // Session storage keys
  const SESSION_KEY = 'supabase-session'
  const PROFILE_KEY = 'user-profile'

  // Save session to localStorage
  const saveSessionToStorage = (session: Session | null) => {
    if (session) {
      localStorage.setItem(SESSION_KEY, JSON.stringify(session))
    } else {
      localStorage.removeItem(SESSION_KEY)
    }
  }

  // Save profile to localStorage
  const saveProfileToStorage = (profile: UserProfile | null) => {
    if (profile) {
      localStorage.setItem(PROFILE_KEY, JSON.stringify(profile))
    } else {
      localStorage.removeItem(PROFILE_KEY)
    }
  }

  // Load session from localStorage
  const loadSessionFromStorage = (): Session | null => {
    try {
      const stored = localStorage.getItem(SESSION_KEY)
      if (stored) {
        const session = JSON.parse(stored) as Session
        // Check if session is still valid (not expired)
        if (session.expires_at && new Date(session.expires_at * 1000) > new Date()) {
          return session
        } else {
          localStorage.removeItem(SESSION_KEY)
        }
      }
    } catch (error) {
      console.error('Error loading session from storage:', error)
      localStorage.removeItem(SESSION_KEY)
    }
    return null
  }

  // Load profile from localStorage
  const loadProfileFromStorage = (): UserProfile | null => {
    try {
      const stored = localStorage.getItem(PROFILE_KEY)
      return stored ? JSON.parse(stored) as UserProfile : null
    } catch (error) {
      console.error('Error loading profile from storage:', error)
      localStorage.removeItem(PROFILE_KEY)
      return null
    }
  }

  const fetchProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) {
        console.error('Error fetching profile:', error)
        return null
      }

      const profile = data as UserProfile
      // Save to localStorage
      saveProfileToStorage(profile)
      return profile
    } catch (error) {
      console.error('Error fetching profile:', error)
      return null
    }
  }

  const refreshProfile = async () => {
    if (user) {
      const profileData = await fetchProfile(user.id)
      setProfile(profileData)
    }
  }

  useEffect(() => {
    // Initialize from localStorage first for faster loading
    const initializeAuth = async () => {
      // Try to load from localStorage first
      const storedSession = loadSessionFromStorage()
      const storedProfile = loadProfileFromStorage()

      if (storedSession && storedProfile) {
        setSession(storedSession)
        setUser(storedSession.user)
        setProfile(storedProfile)
      }

      // Then get the actual session from Supabase
      const { data: { session } } = await supabase.auth.getSession()

      setSession(session)
      setUser(session?.user ?? null)
      saveSessionToStorage(session)

      if (session?.user) {
        const profileData = await fetchProfile(session.user.id)
        setProfile(profileData)
      } else {
        setProfile(null)
        saveProfileToStorage(null)
      }

      setLoading(false)
    }

    initializeAuth()

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (_, session) => {
      setSession(session)
      setUser(session?.user ?? null)
      saveSessionToStorage(session)

      if (session?.user) {
        const profileData = await fetchProfile(session.user.id)
        setProfile(profileData)
      } else {
        setProfile(null)
        saveProfileToStorage(null)
      }

      setLoading(false)
    })

    return () => subscription.unsubscribe()
  }, [])

  const signInWithGoogle = async () => {
    console.log('Signing in with Google...')
    console.log('Redirecting to:', `${window.location.origin}/`)
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/`
        }
      })
      if (error) throw error
    } catch (error) {
      console.error('Error signing in with Google:', error)
      throw error
    }
  }

  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut()
      if (error) throw error

      // Clear localStorage
      saveSessionToStorage(null)
      saveProfileToStorage(null)
    } catch (error) {
      console.error('Error signing out:', error)
      throw error
    }
  }

  // Utility function to clear all auth data
  const clearAuthData = () => {
    setUser(null)
    setProfile(null)
    setSession(null)
    saveSessionToStorage(null)
    saveProfileToStorage(null)
  }

  const value = {
    user,
    profile,
    session,
    loading,
    signInWithGoogle,
    signOut,
    refreshProfile,
    clearAuthData,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
