import React, { createContext, useContext, useEffect, useState } from 'react'
import { User, Session } from '@supabase/supabase-js'
import { supabase, UserProfile } from '@/lib/supabase'

interface AuthContextType {
  user: User | null
  profile: UserProfile | null
  session: Session | null
  loading: boolean
  signInWithGoogle: () => Promise<void>
  signOut: () => Promise<void>
  refreshProfile: () => Promise<void>
  clearAuthData: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)

  // Session storage keys
  const SESSION_KEY = 'supabase-session'
  const PROFILE_KEY = 'user-profile'

  // Save session to localStorage
  const saveSessionToStorage = (session: Session | null) => {
    if (session) {
      localStorage.setItem(SESSION_KEY, JSON.stringify(session))
    } else {
      localStorage.removeItem(SESSION_KEY)
    }
  }

  // Save profile to localStorage
  const saveProfileToStorage = (profile: UserProfile | null) => {
    if (profile) {
      localStorage.setItem(PROFILE_KEY, JSON.stringify(profile))
    } else {
      localStorage.removeItem(PROFILE_KEY)
    }
  }

  // Load session from localStorage
  const loadSessionFromStorage = (): Session | null => {
    try {
      const stored = localStorage.getItem(SESSION_KEY)
      if (stored) {
        const session = JSON.parse(stored) as Session
        // Check if session is still valid (not expired)
        if (session.expires_at && new Date(session.expires_at * 1000) > new Date()) {
          return session
        } else {
          localStorage.removeItem(SESSION_KEY)
        }
      }
    } catch (error) {
      console.error('Error loading session from storage:', error)
      localStorage.removeItem(SESSION_KEY)
    }
    return null
  }

  // Load profile from localStorage
  const loadProfileFromStorage = (): UserProfile | null => {
    try {
      const stored = localStorage.getItem(PROFILE_KEY)
      return stored ? JSON.parse(stored) as UserProfile : null
    } catch (error) {
      console.error('Error loading profile from storage:', error)
      localStorage.removeItem(PROFILE_KEY)
      return null
    }
  }

  const fetchProfile = async (userId: string): Promise<UserProfile | null> => {
    try {
      console.log('Fetching profile for user:', userId)

      // Add timeout to prevent hanging
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Profile fetch timeout')), 10000)
      })

      const fetchPromise = supabase
        .from('profiles')
        .select('*')
        .eq('user_id', userId)
        .single()

      const { data, error } = await Promise.race([fetchPromise, timeoutPromise])

      if (error) {
        console.error('Error fetching profile:', error)
        // If profile doesn't exist, that's okay for new users
        if (error.code === 'PGRST116') {
          console.log('No profile found for user, this is normal for new users')
        }
        return null
      }

      const profile = data as UserProfile
      console.log('Profile fetched successfully:', profile)
      // Save to localStorage
      saveProfileToStorage(profile)
      return profile
    } catch (error) {
      console.error('Error fetching profile:', error)
      return null
    }
  }

  const refreshProfile = async () => {
    if (user) {
      const profileData = await fetchProfile(user.id)
      setProfile(profileData)
    }
  }

  useEffect(() => {
    let isMounted = true

    // Safety timeout to prevent infinite loading
    const loadingTimeout = setTimeout(() => {
      if (isMounted) {
        console.warn('Auth initialization timeout, setting loading to false')
        setLoading(false)
      }
    }, 15000) // 15 second timeout

    // Initialize from localStorage first for faster loading
    const initializeAuth = async () => {
      try {
        console.log('Initializing auth...')

        // Try to load from localStorage first
        const storedSession = loadSessionFromStorage()
        const storedProfile = loadProfileFromStorage()

        if (storedSession && storedProfile && isMounted) {
          console.log('Loading from localStorage')
          setSession(storedSession)
          setUser(storedSession.user)
          setProfile(storedProfile)
        }

        // Then get the actual session from Supabase
        console.log('Getting session from Supabase...')
        const { data: { session }, error } = await supabase.auth.getSession()

        if (error) {
          console.error('Error getting session:', error)
          if (isMounted) {
            setLoading(false)
          }
          return
        }

        if (!isMounted) return

        setSession(session)
        setUser(session?.user ?? null)
        saveSessionToStorage(session)

        if (session?.user) {
          console.log('User found, fetching profile...')
          const profileData = await fetchProfile(session.user.id)
          if (isMounted) {
            setProfile(profileData)
          }
        } else {
          console.log('No user session')
          if (isMounted) {
            setProfile(null)
            saveProfileToStorage(null)
          }
        }

        if (isMounted) {
          setLoading(false)
          clearTimeout(loadingTimeout)
        }
      } catch (error) {
        console.error('Error initializing auth:', error)
        if (isMounted) {
          setLoading(false)
          clearTimeout(loadingTimeout)
        }
      }
    }

    initializeAuth()

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (!isMounted) return

      console.log('Auth state changed:', event, session?.user?.email)

      try {
        setSession(session)
        setUser(session?.user ?? null)
        saveSessionToStorage(session)

        if (session?.user) {
          const profileData = await fetchProfile(session.user.id)
          if (isMounted) {
            setProfile(profileData)
          }
        } else {
          if (isMounted) {
            setProfile(null)
            saveProfileToStorage(null)
          }
        }

        if (isMounted) {
          setLoading(false)
        }
      } catch (error) {
        console.error('Error in auth state change:', error)
        if (isMounted) {
          setLoading(false)
        }
      }
    })

    return () => {
      isMounted = false
      clearTimeout(loadingTimeout)
      subscription.unsubscribe()
    }
  }, [])

  const signInWithGoogle = async () => {
    console.log('Signing in with Google...')
    console.log('Redirecting to:', `${window.location.origin}/`)
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/`
        }
      })
      if (error) throw error
    } catch (error) {
      console.error('Error signing in with Google:', error)
      throw error
    }
  }

  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut()
      if (error) throw error

      // Clear localStorage
      saveSessionToStorage(null)
      saveProfileToStorage(null)
    } catch (error) {
      console.error('Error signing out:', error)
      throw error
    }
  }

  // Utility function to clear all auth data
  const clearAuthData = () => {
    setUser(null)
    setProfile(null)
    setSession(null)
    saveSessionToStorage(null)
    saveProfileToStorage(null)
  }

  const value = {
    user,
    profile,
    session,
    loading,
    signInWithGoogle,
    signOut,
    refreshProfile,
    clearAuthData,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
