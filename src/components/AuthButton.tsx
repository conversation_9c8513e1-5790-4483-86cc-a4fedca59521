import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { useAuth } from '@/contexts/AuthContext'
import { LogIn, LogOut, User } from 'lucide-react'
import { Badge } from '@/components/ui/badge'

export const AuthButton: React.FC = () => {
  const { user, profile, loading, signInWithGoogle, signOut } = useAuth()

  if (loading) {
    return (
      <Button disabled variant="outline">
        Loading...
      </Button>
    )
  }

  if (user) {
    return (
      <div className="flex items-center gap-2">
        <div className="flex items-center gap-2">
          <User className="h-4 w-4" />
          <span className="text-sm">{user.email}</span>
          {profile?.plan_status === 'active' && (
            <Badge variant="default" className="text-xs">
              {profile.plan_type}
            </Badge>
          )}
        </div>
        <Button onClick={signOut} variant="outline" size="sm">
          <LogOut className="h-4 w-4 mr-1" />
          Sign Out
        </Button>
      </div>
    )
  }

  return (
    <Button onClick={signInWithGoogle} variant="default">
      <LogIn className="h-4 w-4 mr-2" />
      Sign in with Google
    </Button>
  )
}
