import React, { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { ChevronDown, ChevronUp } from 'lucide-react'

export const AuthTest: React.FC = () => {
  const { user, profile, loading, clearAuthData } = useAuth()
  const [showStorage, setShowStorage] = useState(false)

  const getStorageData = () => {
    const session = localStorage.getItem('supabase-session')
    const profileData = localStorage.getItem('user-profile')
    return {
      session: session ? 'Stored ✓' : 'Not stored ✗',
      profile: profileData ? 'Stored ✓' : 'Not stored ✗',
      sessionData: session ? JSON.parse(session) : null,
      profileData: profileData ? JSON.parse(profileData) : null
    }
  }

  if (loading) {
    return (
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Auth Status</CardTitle>
        </CardHeader>
        <CardContent>
          <p>Loading...</p>
        </CardContent>
      </Card>
    )
  }

  const storageData = getStorageData()

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Auth Status</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <strong>User:</strong> {user ? user.email : 'Not authenticated'}
        </div>

        {profile && (
          <div className="space-y-2">
            <div>
              <strong>Plan Type:</strong> {profile.plan_type || 'None'}
            </div>
            <div>
              <strong>Plan Status:</strong>
              <Badge
                variant={profile.plan_status === 'active' ? 'default' : 'secondary'}
                className="ml-2"
              >
                {profile.plan_status || 'None'}
              </Badge>
            </div>
            {profile.plan_expires_at && (
              <div>
                <strong>Expires:</strong> {new Date(profile.plan_expires_at).toLocaleDateString()}
              </div>
            )}
          </div>
        )}

        {user && !profile && (
          <div className="text-yellow-600">
            Profile not found - check database setup
          </div>
        )}

        {/* LocalStorage Status */}
        <div className="border-t pt-4">
          <Collapsible open={showStorage} onOpenChange={setShowStorage}>
            <CollapsibleTrigger asChild>
              <Button variant="outline" size="sm" className="w-full">
                <span>LocalStorage Status</span>
                {showStorage ? <ChevronUp className="ml-2 h-4 w-4" /> : <ChevronDown className="ml-2 h-4 w-4" />}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-2 space-y-2 text-sm">
              <div>
                <strong>Session:</strong> {storageData.session}
              </div>
              <div>
                <strong>Profile:</strong> {storageData.profile}
              </div>
              {storageData.sessionData && (
                <div>
                  <strong>Session Expires:</strong> {new Date(storageData.sessionData.expires_at * 1000).toLocaleString()}
                </div>
              )}
            </CollapsibleContent>
          </Collapsible>
        </div>

        {/* Debug Actions */}
        {user && (
          <div className="border-t pt-4">
            <Button
              variant="destructive"
              size="sm"
              onClick={clearAuthData}
              className="w-full"
            >
              Clear Auth Data
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
