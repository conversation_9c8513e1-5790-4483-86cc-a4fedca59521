import React from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

export const AuthTest: React.FC = () => {
  const { user, profile, loading } = useAuth()

  if (loading) {
    return (
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Auth Status</CardTitle>
        </CardHeader>
        <CardContent>
          <p>Loading...</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Auth Status</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <strong>User:</strong> {user ? user.email : 'Not authenticated'}
        </div>
        
        {profile && (
          <div className="space-y-2">
            <div>
              <strong>Plan Type:</strong> {profile.plan_type || 'None'}
            </div>
            <div>
              <strong>Plan Status:</strong> 
              <Badge 
                variant={profile.plan_status === 'active' ? 'default' : 'secondary'}
                className="ml-2"
              >
                {profile.plan_status || 'None'}
              </Badge>
            </div>
            {profile.plan_expires_at && (
              <div>
                <strong>Expires:</strong> {new Date(profile.plan_expires_at).toLocaleDateString()}
              </div>
            )}
          </div>
        )}
        
        {user && !profile && (
          <div className="text-yellow-600">
            Profile not found - check database setup
          </div>
        )}
      </CardContent>
    </Card>
  )
}
