"use client";
import {
  Navbar,
  NavBody,
  NavItems,
  MobileNav,
  NavbarLogo,
  NavbarButton,
  MobileNavHeader,
  MobileNavToggle,
  MobileNavMenu,
} from "@/components/ui/resizable-navbar";
import { useState } from "react";
import { PolarEmbedCheckout } from '@polar-sh/checkout/embed';
import { useAuth } from "@/contexts/AuthContext";
import { AuthButton } from "@/components/AuthButton";
import { toast } from "sonner";

export default function NavbarDemo() {
  const navItems = [
    {
      name: "Twitter",
      link: "https://twitter.com/keep_it_running",
    },
  ];

  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { user, signInWithGoogle, refreshProfile } = useAuth();

  const handlePurchase = async () => {
    // Check if user is authenticated
    if (!user) {
      toast.error("Please sign in to make a purchase");
      try {
        await signInWithGoogle();
      } catch (error) {
        console.error('Error signing in:', error);
        toast.error("Failed to sign in. Please try again.");
      }
      return;
    }

    try {
      // Call our backend to create checkout session
      const response = await fetch('http://localhost:8000/create-checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customer_billing_address: {
            country: 'US'
          },
          // Set embed_origin for embedded checkout
          embed_origin: window.location.origin,
          // Pass user ID for external_customer_id
          user_id: user.id
        }),
      });

      const data = await response.json();

      if (data.success && data.checkout?.url) {
        // Use Polar SDK to embed the checkout
        const checkout = await PolarEmbedCheckout.create(data.checkout.url, 'light');

        // Listen for checkout events
        checkout.addEventListener('success', (event) => {
          console.log('Purchase successful!', event.detail);
          toast.success('Purchase completed successfully!');
          // Refresh user profile to get updated subscription status
          refreshProfile();
        });

        checkout.addEventListener('close', () => {
          console.log('Checkout closed');
        });

      } else {
        toast.error('Error creating checkout: ' + (data.error || 'Unknown error'));
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Error creating checkout. Please try again.');
    }
  };

  return (
    <div className="relative w-full">
      <Navbar>
        {/* Desktop Navigation */}
        <NavBody>
          <NavbarLogo />
          <NavItems items={navItems} />
          <div className="flex items-center gap-4">
            <NavbarButton
              as="button"
              onClick={handlePurchase}
              variant="gradient"
            >
              Purchase
            </NavbarButton>
            <AuthButton />
          </div>
        </NavBody>

        {/* Mobile Navigation */}
        <MobileNav>
          <MobileNavHeader>
            <NavbarLogo />
            <MobileNavToggle
              isOpen={isMobileMenuOpen}
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            />
          </MobileNavHeader>

          <MobileNavMenu
            isOpen={isMobileMenuOpen}
            onClose={() => setIsMobileMenuOpen(false)}
          >
            {navItems.map((item, idx) => (
              <a
                key={`mobile-link-${idx}`}
                href={item.link}
                onClick={() => setIsMobileMenuOpen(false)}
                className="relative text-neutral-600 dark:text-neutral-300"
              >
                <span className="block">{item.name}</span>
              </a>
            ))}
            <div className="flex w-full flex-col gap-4">
              <NavbarButton
                as="button"
                onClick={() => {
                  setIsMobileMenuOpen(false);
                  handlePurchase();
                }}
                variant="gradient"
                className="w-full"
              >
                Purchase
              </NavbarButton>
              <div className="w-full">
                <AuthButton />
              </div>
            </div>
          </MobileNavMenu>
        </MobileNav>
      </Navbar>
      {/* Navbar */}
    </div>
  );
}