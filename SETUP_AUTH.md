# Google Auth + Supabase + Polar Integration Setup

This guide walks you through setting up Google authentication with Supabase and integrating it with Polar subscriptions.

## 1. Supabase Setup

### Create a Supabase Project
1. Go to [supabase.com](https://supabase.com) and create a new project
2. Note down your project URL and anon key from Settings > API

### Configure Google OAuth
1. In your Supabase dashboard, go to Authentication > Providers
2. Enable Google provider
3. Add your Google OAuth credentials (Client ID and Secret)
4. Set the redirect URL to: `https://your-project.supabase.co/auth/v1/callback`

### Run Database Migration
1. In your Supabase dashboard, go to SQL Editor
2. Run the migration from `supabase/migrations/001_create_user_profiles.sql`

## 2. Environment Variables

### Frontend (.env)
```
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### Backend (.env)
```
# Polar SDK Configuration
POLAR_ACCESS_TOKEN=your_polar_access_token
PRODUCT_PLAN_ID=your_product_plan_id

# Supabase Configuration
SUPABASE_URL=your_supabase_project_url
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# FastAPI Configuration
ENVIRONMENT=development
DEBUG=True
```

## 3. Google OAuth Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API
4. Go to Credentials > Create Credentials > OAuth 2.0 Client ID
5. Add authorized redirect URIs:
   - `https://your-project.supabase.co/auth/v1/callback`
   - `http://localhost:8080` (for development)

## 4. Polar Webhook Setup

1. In your Polar dashboard, go to Webhooks
2. Add a new webhook endpoint: `https://your-backend-url/webhook/polar`
3. Subscribe to `subscription.updated` events

## 5. Installation & Running

### Frontend
```bash
npm install
npm run dev
```

### Backend
```bash
pip install -r requirements.txt
python app.py
```

## 6. How It Works

1. **Authentication Flow:**
   - User clicks "Sign in with Google"
   - Supabase handles OAuth flow
   - User profile is automatically created in `user_profiles` table

2. **Purchase Flow:**
   - User must be authenticated to purchase
   - User ID is passed to backend as `external_customer_id` to Polar
   - Polar checkout is embedded in the frontend

3. **Subscription Management:**
   - Polar sends webhook events to `/webhook/polar`
   - Backend updates user profile with subscription status
   - Frontend shows subscription status in auth button

## 7. Database Schema

The `user_profiles` table includes:
- `id`: UUID (references auth.users)
- `email`: User email
- `plan_type`: 'free' | 'premium' | 'pro'
- `plan_status`: 'active' | 'inactive' | 'cancelled' | 'expired'
- `plan_expires_at`: Subscription expiry date
- `created_at` / `updated_at`: Timestamps

## 8. Security Notes

- Frontend uses Supabase anon key (safe for client-side)
- Backend uses service role key (keep secret)
- RLS policies ensure users can only access their own data
- Webhook endpoint should be secured in production (verify signatures)
